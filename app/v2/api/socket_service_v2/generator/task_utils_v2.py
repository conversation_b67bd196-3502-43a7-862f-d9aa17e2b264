"""
Task Utilities V2 for Socket Service V2

Collection-based task and story utilities that handle saving to collections
and returning collection IDs instead of individual items.
"""

from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Union
from bson.objectid import ObjectId
import asyncio
import uuid

from app.shared.db_enums import TaskStatus, QuizType, InputType, GenerationType
from app.shared.utils.logger import setup_new_logging
from app.shared.database import retry_db_operation
from app.shared.models.user import UserTenantDB
from app.v2.api.socket_service_v2.generator.prompt_maker_v2 import generate as generate_v2

# Import v1 generation functions for exact compatibility
from app.v2.api.socket_service_v2.generator.audiogen import generate_audio
from app.v2.api.socket_service_v2.generator.imagen import generate_image

# Configure logging
logger = setup_new_logging(__name__)

# Retry configuration
MAX_RETRIES = 3
INITIAL_RETRY_DELAY = 1.0
MAX_RETRY_DELAY = 30.0


def _map_task_type(prompt_maker_type: str) -> str:
    """
    Map prompt_maker.py task types to database QuizType enum values.

    Args:
        prompt_maker_type: Task type from prompt_maker.py

    Returns:
        Mapped QuizType enum value
    """
    type_mapping = {
        "single_choice": QuizType.SINGLE_CHOICE.value,
        "multiple_choice": QuizType.MULTIPLE_CHOICE.value,
        "image_identification": QuizType.IMAGE_IDENTIFICATION.value,
        "image_identify": QuizType.IMAGE_IDENTIFICATION.value,  # Keep old mapping for compatibility
        "speak_word": QuizType.SPEAK_WORD.value,
        "true_false": QuizType.SINGLE_CHOICE.value,  # Map true_false to single_choice
        "fill_in_blank": QuizType.ANSWER_IN_WORD.value,  # Map fill_in_blank to answer_in_word
        "visual_question": QuizType.IMAGE_IDENTIFICATION.value,  # Map visual_question to image_identification
        "pronunciation": QuizType.SPEAK_WORD.value,  # Map pronunciation to speak_word
        "audio_identification": QuizType.SPEAK_WORD.value,  # Map audio_identification to speak_word
    }

    return type_mapping.get(prompt_maker_type, QuizType.SINGLE_CHOICE.value)


def serialize_usage_metadata(usage_metadata: Any) -> Dict[str, Any]:
    """
    Convert usage metadata to a MongoDB-serializable dictionary.

    Args:
        usage_metadata: Usage metadata object from Gemini API

    Returns:
        Serializable dictionary
    """
    if not usage_metadata:
        return {}

    try:
        # If it has model_dump method (Pydantic model)
        if hasattr(usage_metadata, 'model_dump'):
            return usage_metadata.model_dump()
        # If it has dict method
        elif hasattr(usage_metadata, 'dict'):
            return usage_metadata.dict()
        # If it's already a dict
        elif isinstance(usage_metadata, dict):
            return usage_metadata
        # Try to convert to dict
        else:
            return dict(usage_metadata) if usage_metadata else {}
    except Exception as e:
        logger.warning(f"Failed to serialize usage metadata: {e}")
        return {"serialization_error": str(e)}


async def retry_with_exponential_backoff(func, *args, **kwargs):
    """Retry function with exponential backoff."""
    delay = INITIAL_RETRY_DELAY
    
    for attempt in range(MAX_RETRIES):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            if attempt == MAX_RETRIES - 1:
                raise e
            
            logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {delay}s...")
            await asyncio.sleep(delay)
            delay = min(delay * 2, MAX_RETRY_DELAY)


async def save_task_collection_and_items_with_priority(
    current_user: UserTenantDB,
    session_id: str,
    tasks_data: Dict[str, Any],
    collection_id: Optional[str] = None,
    audio_storage_info: Optional[Dict[str, Any]] = None,
    socketio_server: Optional[Any] = None,
    use_background_tasks: bool = True
) -> Dict[str, Any]:
    """
    Save tasks and stories with priority-based parallel processing.

    Priority Processing:
    1. INSTANT: Text-based tasks (single_choice, multiple_choice) - saved immediately
    2. PARALLEL: Media tasks (image_identification, speak_word) - generated in background
    3. PARALLEL: Story images - generated in background with lowest priority

    Args:
        current_user: Current user context with database access
        session_id: Session identifier
        tasks_data: Output from prompt_maker_v2.py with tasks and stories
        collection_id: Optional existing collection ID, creates new one if None
        audio_storage_info: Optional MinIO storage information
        socketio_server: Optional SocketIO server for real-time updates
        use_background_tasks: Whether to use background processing

    Returns:
        Dictionary with task_set_id (collection ID) and instant text tasks ready
    """
    try:
        if not tasks_data or not tasks_data.get("tasks"):
            return {
                "status": "error",
                "error": "No tasks data provided",
                "task_set_id": None
            }

        # Generate collection ID if not provided (this will be the task_set_id)
        if not collection_id:
            collection_id = str(uuid.uuid4())

        tasks = tasks_data["tasks"]
        stories = tasks_data.get("stories", [])
        optimization_stats = tasks_data.get("optimization_stats", {})
        usage_metadata = serialize_usage_metadata(tasks_data.get("usage_metadata", {}))
        title = tasks_data.get("title", "Generated Task Set V2")

        # Separate tasks by priority for parallel processing
        text_tasks = []  # Highest priority - instant
        media_tasks = []  # Medium priority - parallel background

        for task in tasks:
            task_type = task.get("type", "single_choice")
            if task_type in ["single_choice", "multiple_choice", "true_false", "fill_in_blank"]:
                text_tasks.append(task)
            else:
                media_tasks.append(task)

        logger.info(f"💾 Priority processing: {len(text_tasks)} text tasks (instant), {len(media_tasks)} media tasks (parallel), {len(stories)} stories (background)")

        # Create task set document using existing task_sets collection structure
        task_set_id = ObjectId()
        task_set_doc = {
            "_id": task_set_id,
            "user_id": ObjectId(current_user.user.id),
            "session_id": session_id,
            "title": title,
            "input_type": InputType.AUDIO,
            "tasks": [],  # Will be populated with text tasks first, then media tasks
            "stories": [],  # Will be populated with stories (images generated in background)
            "total_tasks": len(tasks),
            "total_stories": len(stories),
            "text_tasks_ready": len(text_tasks),
            "media_tasks_pending": len(media_tasks),
            "attempted_tasks": 0,
            "total_verified": 0,
            "status": TaskStatus.PENDING,
            "gentype":GenerationType.PRIMARY.value,
            "has_follow_up": False,
            "total_score": 0,
            "scored": 0,
            "attempts_count": 0,
            "created_at": datetime.now(timezone.utc),
            # V2 specific metadata
            "v2_collection_id": collection_id,
            "optimization_metadata": optimization_stats,
            "usage_metadata": usage_metadata,
            "service_version": "v2",
            "priority_processing": {
                "text_tasks_count": len(text_tasks),
                "media_tasks_count": len(media_tasks),
                "stories_count": len(stories),
                "processing_status": "text_ready_media_pending"
            }
        }

        # Add audio storage information if provided (same as v1)
        if audio_storage_info:
            # Store complete MinIO object information in input_content (like v1)
            input_content = {
                "object_name": audio_storage_info.get("object_name"),
                "bucket_name": audio_storage_info.get("bucket_name"),
                "object_path": audio_storage_info.get("object_path"),
                "file_name": audio_storage_info.get("file_name"),
                "content_type": audio_storage_info.get("content_type", "audio/wav"),
                "size_bytes": audio_storage_info.get("size_bytes"),
                "folder": audio_storage_info.get("folder", "recordings_v2"),
                "session_id": session_id,
                "created_at": audio_storage_info.get("created_at"),
                "file_extension": audio_storage_info.get("file_extension", ".wav"),
            }
            task_set_doc["input_content"] = input_content
            logger.debug(f"Adding input_content to task set (same as v1)")
        else:
            logger.debug("No audio_storage_info provided, input_content will not be added")

        # PRIORITY 1: Process text-based tasks INSTANTLY (no media needed)
        total_score = 0
        instant_tasks = []
        pending_media_tasks = []

        # Process text tasks first - these go to task_items collection immediately
        for task_data in text_tasks:
            try:
                task_item_id = ObjectId()
                task_type = _map_task_type(task_data.get("type", "single_choice"))
                question_data = task_data.get("question", {})

                task_item = {
                    "_id": task_item_id,
                    "task_set_id": task_set_id,
                    "user_id": ObjectId(current_user.user.id),
                    "session_id": session_id,
                    "type": task_type,
                    "title": task_data.get("title", "Generated Task"),
                    "question": {
                        "text": question_data.get("text", ""),
                        "translated_text": question_data.get("translated_text", ""),
                        "options": question_data.get("options", {}),
                        "answer_hint": question_data.get("answer_hint", ""),
                        "metadata": question_data.get("metadata", {})
                    },
                    "correct_answer": {
                        "value": question_data.get("answer", ""),
                        "type": "single" if task_type == QuizType.SINGLE_CHOICE.value else
                               "speak" if task_type == QuizType.SPEAK_WORD.value else "multiple"
                    },
                    "user_answer": None,
                    "status": TaskStatus.PENDING.value,
                    "result": None,
                    "remark": None,
                    "total_score": task_data.get("total_score", 10),
                    "scored": 0,
                    "submitted": False,
                    "submitted_at": None,
                    "attempts_count": 0,
                    "difficulty_level": task_data.get("difficulty_level", 2),
                    "metadata": {
                        "_v2_optimized": task_data.get("_v2_optimized", False),
                        "_media_excluded": task_data.get("_media_excluded", False),
                        "_priority": "instant",
                        "_media_ready": True
                    },
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }

                # Generate audio for options if the task has options
                await _generate_options_audio(current_user, task_item, task_item["_id"])

                instant_tasks.append(task_item)
                total_score += task_item["total_score"]

            except Exception as e:
                logger.error(f"Error processing instant task: {e}")
                continue

        # Save instant text tasks to task_items collection immediately
        if instant_tasks:
            await current_user.async_db.task_items.insert_many(instant_tasks)
            logger.info(f"✅ Saved {len(instant_tasks)} instant text tasks to task_items collection")

        # PRIORITY 2: Save media tasks to database immediately (media will be generated in background)
        for task_data in media_tasks:
            try:
                task_item_id = ObjectId()
                task_type = _map_task_type(task_data.get("type", "single_choice"))
                question_data = task_data.get("question", {})

                task_item = {
                    "_id": task_item_id,
                    "task_set_id": task_set_id,
                    "user_id": ObjectId(current_user.user.id),
                    "session_id": session_id,
                    "type": task_type,
                    "title": task_data.get("title", "Generated Task"),
                    "question": {
                        "text": question_data.get("text", ""),
                        "translated_text": question_data.get("translated_text", ""),
                        "options": question_data.get("options", {}),
                        "answer_hint": question_data.get("answer_hint", ""),
                        "metadata": question_data.get("metadata", {})
                    },
                    "correct_answer": {
                        "value": question_data.get("answer", ""),
                        "type": "single" if task_type == QuizType.SINGLE_CHOICE.value else
                               "speak" if task_type == QuizType.SPEAK_WORD.value else "multiple"
                    },
                    "user_answer": None,
                    "status": TaskStatus.PENDING.value,
                    "result": None,
                    "remark": None,
                    "total_score": task_data.get("total_score", 10),
                    "scored": 0,
                    "submitted": False,
                    "submitted_at": None,
                    "attempts_count": 0,
                    "difficulty_level": task_data.get("difficulty_level", 2),
                    "metadata": {
                        "_v2_optimized": task_data.get("_v2_optimized", False),
                        "_media_excluded": task_data.get("_media_excluded", False),
                        "_priority": "media_pending",
                        "_media_ready": False
                    },
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }

                # Generate audio for options if the task has options
                await _generate_options_audio(current_user, task_item, task_item["_id"])

                pending_media_tasks.append(task_item)
                total_score += task_item["total_score"]

            except Exception as e:
                logger.error(f"Error preparing media task: {e}")
                continue

        # Save media tasks to task_items collection immediately (media will be generated in background)
        if pending_media_tasks:
            await current_user.async_db.task_items.insert_many(pending_media_tasks)
            logger.info(f"✅ Saved {len(pending_media_tasks)} media tasks to task_items collection (media pending)")

        # PRIORITY 3: Process stories for background image generation
        instant_stories = []
        for story_data in stories:
            try:
                story_item_id = ObjectId()
                story_item = {
                    "_id": story_item_id,
                    "task_set_id": task_set_id,
                    "user_id": ObjectId(current_user.user.id),
                    "session_id": session_id,
                    "stage": story_data.get("stage", 1),
                    "script": story_data.get("script", ""),
                    "image": story_data.get("image", ""),
                    "metadata": {
                        **story_data.get("metadata", {}),
                        "_priority": "background_image",
                        "_image_ready": False
                    },
                    "task_title": story_data.get("task_title", ""),
                    "task_type": story_data.get("task_type", ""),
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }

                instant_stories.append(story_item)

            except Exception as e:
                logger.error(f"Error processing story: {e}")
                continue

        # Save instant stories to story_steps collection (images will be generated in background)
        if instant_stories:
            await current_user.async_db.story_steps.insert_many(instant_stories)
            logger.info(f"✅ Saved {len(instant_stories)} stories to story_steps collection (images pending)")

        # Update task set with references and metadata - SAME FORMAT AS V1
        all_processed_tasks = instant_tasks + pending_media_tasks
        task_set_doc["tasks"] = [str(task["_id"]) for task in all_processed_tasks]  # V1 format: simple array of task IDs
        task_set_doc["stories"] = [str(story["_id"]) for story in instant_stories]  # V1 format: simple array of story IDs
        task_set_doc["total_score"] = total_score

        # Save task set to existing task_sets collection
        await current_user.async_db.task_sets.insert_one(task_set_doc)

        # Start background processing for media tasks and story images
        if use_background_tasks and (pending_media_tasks or instant_stories):
            try:
                # Create background task with proper error handling
                background_task = asyncio.create_task(_process_media_in_background(
                    current_user, task_set_id, pending_media_tasks, instant_stories, socketio_server
                ))
                # Add callback to log any exceptions
                background_task.add_done_callback(
                    lambda task: logger.error(f"❌ Background task failed: {task.exception()}")
                    if task.exception() else logger.debug(f"✅ Background task completed successfully")
                )
                logger.info(f"🔄 Started background processing for {len(pending_media_tasks)} media tasks and {len(instant_stories)} story images")
            except Exception as e:
                logger.error(f"❌ Failed to start background processing: {e}")
                # Continue without background processing
                pass

        logger.info(f"✅ Instant response ready: {len(instant_tasks)} text tasks available immediately")

        return {
            "status": "success",
            "task_set_id": str(task_set_id),  # Return MongoDB ObjectId as string (like v1)
            "collection_metadata": {
                "total_task_sets": 1,
                "total_tasks": len(all_processed_tasks),
                "total_stories": len(instant_stories),
                "instant_tasks_ready": len(instant_tasks),
                "media_tasks_pending": len(pending_media_tasks),
                "stories_pending_images": len(instant_stories),
                "optimization_stats": optimization_stats,
                "v2_collection_id": collection_id,  # Keep UUID for v2 tracking
                "service_version": "v2",
                "priority_processing": True
            },
            "instant_tasks": instant_tasks,  # Ready immediately
            "pending_media_tasks": len(pending_media_tasks),  # Being processed in background
            "pending_stories": len(instant_stories)  # Images being generated in background
        }

    except Exception as e:
        logger.error(f"❌ Error saving task collection: {e}")
        return {
            "status": "error",
            "error": str(e),
            "task_set_id": None
        }


async def _process_media_in_background(
    current_user: UserTenantDB,
    task_set_id: ObjectId,
    pending_media_tasks: List[Dict[str, Any]],
    pending_stories: List[Dict[str, Any]],
    socketio_server: Optional[Any] = None
):
    """
    Process media tasks and story images in parallel background.

    Priority:
    1. PARALLEL: Generate media for tasks (medium priority)
    2. PARALLEL: Generate images for stories (lowest priority)
    """
    try:
        logger.info(f"🔄 Background processing started for task_set {task_set_id} with {len(pending_media_tasks)} media tasks and {len(pending_stories)} stories")

        # Log task details for debugging
        for i, task in enumerate(pending_media_tasks):
            logger.debug(f"📋 Media task {i+1}: {task.get('title', 'Unknown')} ({task.get('type', 'Unknown')})")

        for i, story in enumerate(pending_stories):
            logger.debug(f"📖 Story {i+1}: Stage {story.get('stage', 'Unknown')}")

        # PRIORITY 1: Start task media generation immediately (highest priority)
        task_media_tasks = []
        if pending_media_tasks:
            logger.info(f"🚀 Starting PRIORITY 1: {len(pending_media_tasks)} task media generations")
            for task_item in pending_media_tasks:
                # Start each task media generation independently - don't wait
                task = asyncio.create_task(_generate_task_media_independent(current_user, task_item, socketio_server))
                task_media_tasks.append(task)
                # Add error callback for individual task
                task.add_done_callback(
                    lambda t, task_id=task_item["_id"]: logger.error(f"❌ Task media generation failed for {task_id}: {t.exception()}")
                    if t.exception() else logger.debug(f"✅ Task media generation completed for {task_id}")
                )

        # PRIORITY 2: Start story media generation after task media (lower priority)
        story_media_tasks = []
        if pending_stories:
            logger.info(f"🚀 Starting PRIORITY 2: {len(pending_stories)} story media generations")
            for story_item in pending_stories:
                # Start each story media generation independently - don't wait
                task = asyncio.create_task(_generate_story_media_independent(current_user, story_item, socketio_server))
                story_media_tasks.append(task)
                # Add error callback for individual story
                task.add_done_callback(
                    lambda t, story_id=story_item["_id"]: logger.error(f"❌ Story media generation failed for {story_id}: {t.exception()}")
                    if t.exception() else logger.debug(f"✅ Story media generation completed for {story_id}")
                )

        # Don't wait for any tasks to complete - they run independently
        total_background_tasks = len(task_media_tasks) + len(story_media_tasks)
        logger.info(f"🔄 Started {total_background_tasks} independent background tasks ({len(task_media_tasks)} tasks + {len(story_media_tasks)} stories)")

        # Update task set status to indicate all media is ready
        try:
            await current_user.async_db.task_sets.update_one(
                {"_id": task_set_id},
                {
                    "$set": {
                        "priority_processing.processing_status": "all_media_ready",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"✅ Updated task_set {task_set_id} status to all_media_ready")
        except Exception as db_error:
            logger.error(f"❌ Failed to update task_set status: {db_error}")

        # Notify via WebSocket if available
        if socketio_server:
            try:
                # Get the actual AsyncServer instance for emitting
                sio_instance = None
                if hasattr(socketio_server, 'sio'):
                    # It's the SocketIOServer wrapper - get the sio attribute
                    sio_instance = socketio_server.sio
                    logger.debug("Using SocketIOServer.sio for WebSocket notification")
                elif hasattr(socketio_server, 'emit'):
                    # It's the AsyncServer directly
                    sio_instance = socketio_server
                    logger.debug("Using AsyncServer directly for WebSocket notification")
                else:
                    logger.error(f"❌ Unknown socketio_server type: {type(socketio_server)}")
                    return

                if sio_instance:
                    await sio_instance.emit(
                        "media_processing_complete",
                        {
                            "task_set_id": str(task_set_id),
                            "status": "all_media_ready",
                            "user_id": str(current_user.user.id),
                            "timestamp": datetime.now().isoformat()
                        }
                    )
                    logger.info(f"✅ Sent WebSocket notification for task_set {task_set_id}")
                else:
                    logger.error("❌ Could not get valid sio instance for WebSocket notification")
            except Exception as ws_error:
                logger.error(f"❌ Failed to send WebSocket notification: {ws_error}")
                logger.error(f"❌ socketio_server type: {type(socketio_server)}")
                logger.error(f"❌ socketio_server attributes: {dir(socketio_server)}")

    except Exception as e:
        logger.error(f"❌ Background processing failed for task_set {task_set_id}: {e}")


async def _generate_task_media_independent(
    current_user: UserTenantDB,
    task_item: Dict[str, Any],
    socketio_server: Optional[Any] = None
):
    """Generate media for a single task independently and store immediately."""
    try:
        task_id = task_item["_id"]
        task_type = str(task_item["type"])
        question = task_item.get("question", {})

        logger.info(f"🎨 PRIORITY 1: Generating media for task {task_id} ({task_type})")

        # Generate media based on task type and store immediately
        if task_type in ["image_identification", "visual_question"]:
            # Generate image for visual tasks
            keyword = question.get("answer_hint") or question.get("answer") or task_item.get("title", "")
            if keyword:
                await _generate_and_store_task_image(current_user, keyword, task_id, socketio_server)
            else:
                logger.error(f"❌ No keyword found for image task {task_id}")
                await _mark_task_media_failed(current_user, task_id, "No keyword found for image task")

        elif task_type in ["speak_word", "pronunciation", "audio_identification"]:
            # Generate audio for audio tasks
            keyword = question.get("answer_hint") or question.get("answer") or task_item.get("title", "")
            if keyword:
                await _generate_and_store_task_audio(current_user, keyword, task_id, socketio_server)
            else:
                logger.error(f"❌ No keyword found for audio task {task_id}")
                await _mark_task_media_failed(current_user, task_id, "No keyword found for audio task")

        else:
            # For choice tasks, no media is needed - mark as ready immediately
            await current_user.async_db.task_items.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "metadata._media_ready": True,
                        "metadata._priority": "no_media_needed",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"✅ Task {task_id} marked as ready (no media needed for {task_type})")

            # Send immediate notification
            await _send_websocket_notification(socketio_server, "task_media_ready", {
                "task_id": str(task_id),
                "task_type": task_type,
                "media_url": None,
                "media_needed": False,
                "user_id": str(current_user.user.id),
                "timestamp": datetime.now().isoformat()
            })

    except Exception as e:
        logger.error(f"❌ Failed to generate media for task {task_item.get('_id')}: {e}")
        await _mark_task_media_failed(current_user, task_item.get('_id'), str(e))


@retry_db_operation(max_retries=3, delay=2.0, backoff=2.0)
async def _generate_and_store_task_image(
    current_user: UserTenantDB,
    keyword: str,
    task_id: ObjectId,
    socketio_server: Optional[Any] = None
):
    """Generate and immediately store task image."""
    try:
        logger.info(f"🖼️ Generating image for task {task_id} with keyword: {keyword}")

        # Generate image using v1 function
        _file_text, file_info, usage_metadata = await generate_image(current_user, keyword)

        if file_info:
            # Store image metadata immediately
            await current_user.async_db.task_items.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "question.metadata": file_info,
                        "metadata._media_ready": True,
                        "metadata._priority": "image_ready",
                        "updated_at": datetime.now(timezone.utc),
                        "usage": usage_metadata.model_dump() if hasattr(usage_metadata, 'model_dump') else usage_metadata
                    }
                }
            )
            logger.info(f"✅ Image generated and stored for task {task_id}: {file_info.get('url')}")

            # Send immediate notification
            await _send_websocket_notification(socketio_server, "task_image_ready", {
                "task_id": str(task_id),
                "image_url": file_info.get('url'),
                "user_id": str(current_user.user.id),
                "timestamp": datetime.now().isoformat()
            })
        else:
            logger.error(f"❌ No image file_info received for task {task_id}")
            await _mark_task_media_failed(current_user, task_id, "No image data received")

    except Exception as e:
        logger.error(f"❌ Failed to generate image for task {task_id}: {e}")
        await _mark_task_media_failed(current_user, task_id, str(e))


@retry_db_operation(max_retries=3, delay=2.0, backoff=2.0)
async def _generate_and_store_task_audio(
    current_user: UserTenantDB,
    keyword: str,
    task_id: ObjectId,
    socketio_server: Optional[Any] = None
):
    """Generate and immediately store task audio."""
    try:
        logger.info(f"🔊 Generating audio for task {task_id} with keyword: {keyword}")

        # Generate audio using v1 function
        _file_text, file_info, usage_metadata = await generate_audio(current_user, keyword, "audio_prompt")

        if file_info:
            # Store audio metadata immediately
            await current_user.async_db.task_items.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "question.metadata": file_info,
                        "metadata._media_ready": True,
                        "metadata._priority": "audio_ready",
                        "updated_at": datetime.now(timezone.utc),
                        "usage": usage_metadata.model_dump() if hasattr(usage_metadata, 'model_dump') else usage_metadata
                    }
                }
            )
            logger.info(f"✅ Audio generated and stored for task {task_id}: {file_info.get('url')}")

            # Send immediate notification
            await _send_websocket_notification(socketio_server, "task_audio_ready", {
                "task_id": str(task_id),
                "audio_url": file_info.get('url'),
                "user_id": str(current_user.user.id),
                "timestamp": datetime.now().isoformat()
            })
        else:
            logger.error(f"❌ No audio file_info received for task {task_id}")
            await _mark_task_media_failed(current_user, task_id, "No audio data received")

    except Exception as e:
        logger.error(f"❌ Failed to generate audio for task {task_id}: {e}")
        await _mark_task_media_failed(current_user, task_id, str(e))


@retry_db_operation(max_retries=3, delay=1.0, backoff=2.0)
async def _mark_task_media_failed(current_user: UserTenantDB, task_id: ObjectId, error_msg: str):
    """Mark task media generation as failed."""
    try:
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "metadata._media_ready": False,
                    "metadata._priority": "media_failed",
                    "metadata.error": error_msg,
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )
        logger.error(f"❌ Marked task {task_id} media as failed: {error_msg}")
    except Exception as db_error:
        logger.error(f"❌ Failed to mark task {task_id} as failed: {db_error}")


async def _generate_story_media_independent(
    current_user: UserTenantDB,
    story_item: Dict[str, Any],
    socketio_server: Optional[Any] = None
):
    """Generate image and audio for a single story independently, storing each as soon as generated."""
    try:
        story_id = story_item["_id"]
        stage = story_item["stage"]
        image_description = story_item.get("image", "")
        script_text = story_item.get("script", "")

        logger.info(f"🖼️ PRIORITY 2: Generating media for story {story_id} stage {stage}")

        # Start image and audio generation independently - don't wait for each other
        image_task = None
        audio_task = None

        # Start image generation if needed
        if image_description:
            image_task = asyncio.create_task(_generate_and_store_story_image(
                current_user, story_id, stage, image_description, socketio_server
            ))
            image_task.add_done_callback(
                lambda t: logger.error(f"❌ Story image generation failed for {story_id}: {t.exception()}")
                if t.exception() else logger.debug(f"✅ Story image generation completed for {story_id}")
            )
        else:
            # No image description - mark as ready immediately
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$set": {
                        "metadata._image_ready": True,
                        "metadata._priority": "no_image_needed",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"✅ Story {story_id} image marked as ready (no description provided)")

        # Start audio generation if needed
        if script_text:
            audio_task = asyncio.create_task(_generate_and_store_story_audio(
                current_user, story_id, stage, script_text, socketio_server
            ))
            audio_task.add_done_callback(
                lambda t: logger.error(f"❌ Story audio generation failed for {story_id}: {t.exception()}")
                if t.exception() else logger.debug(f"✅ Story audio generation completed for {story_id}")
            )
        else:
            # No script text - mark as ready immediately
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$set": {
                        "audio_metadata._audio_ready": True,
                        "audio_metadata._priority": "no_audio_needed",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"✅ Story {story_id} audio marked as ready (no script provided)")

        # Don't wait for tasks to complete - they run independently
        logger.info(f"🚀 Started independent generation for story {story_id} (image: {image_task is not None}, audio: {audio_task is not None})")

    except Exception as e:
        logger.error(f"❌ Failed to start media generation for story {story_item.get('_id')}: {e}")


@retry_db_operation(max_retries=3, delay=2.0, backoff=2.0)
async def _generate_and_store_story_image(
    current_user: UserTenantDB,
    story_id: ObjectId,
    stage: int,
    image_description: str,
    socketio_server: Optional[Any] = None
):
    """Generate and immediately store story image."""
    try:
        logger.info(f"🖼️ Generating image for story {story_id} stage {stage}: {image_description[:50]}...")

        _text, file_info, image_usage = await generate_image(current_user, image_description)

        if file_info:
            # Store image metadata immediately
            complete_metadata = {
                **file_info,  # Complete metadata from MinIO
                "_image_ready": True,
                "usage": image_usage.model_dump() if hasattr(image_usage, 'model_dump') else image_usage,
                "_priority": "image_ready",
                "generated_at": datetime.now(timezone.utc)
            }
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$set": {
                        "metadata": complete_metadata,
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"✅ Story image generated and stored for {story_id}: {file_info.get('url')}")

            # Send immediate notification
            await _send_websocket_notification(socketio_server, "story_image_ready", {
                "story_id": str(story_id),
                "stage": stage,
                "image_url": file_info.get('url'),
                "user_id": str(current_user.user.id),
                "timestamp": datetime.now().isoformat()
            })
        else:
            logger.error(f"❌ No image data received for story {story_id}")
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$set": {
                        "metadata._image_ready": False,
                        "metadata._priority": "image_failed",
                        "metadata.error": "No image data received",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )

    except Exception as e:
        logger.error(f"❌ Failed to generate story image for {story_id}: {e}")
        await current_user.async_db.story_steps.update_one(
            {"_id": story_id},
            {
                "$set": {
                    "metadata._image_ready": False,
                    "metadata._priority": "image_failed",
                    "metadata.error": str(e),
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )


@retry_db_operation(max_retries=3, delay=2.0, backoff=2.0)
async def _generate_and_store_story_audio(
    current_user: UserTenantDB,
    story_id: ObjectId,
    stage: int,
    script_text: str,
    socketio_server: Optional[Any] = None
):
    """Generate and immediately store story audio."""
    try:
        logger.info(f"🔊 Generating audio for story {story_id} stage {stage}: {script_text[:50]}...")

        _file_text, audio_file_info, audio_usage = await generate_audio(current_user, script_text)

        if audio_file_info:
            # Store audio metadata immediately
            audio_metadata = {
                **audio_file_info,  # Complete metadata from MinIO
                "_audio_ready": True,
                "_priority": "audio_ready",
                "audio_usage": audio_usage.model_dump() if hasattr(audio_usage, 'model_dump') else audio_usage,
                "generated_at": datetime.now(timezone.utc)
            }
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$set": {
                        "audio_metadata": audio_metadata,
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"✅ Story audio generated and stored for {story_id}: {audio_file_info.get('url')}")

            # Send immediate notification
            await _send_websocket_notification(socketio_server, "story_audio_ready", {
                "story_id": str(story_id),
                "stage": stage,
                "audio_url": audio_file_info.get('url'),
                "user_id": str(current_user.user.id),
                "timestamp": datetime.now().isoformat()
            })
        else:
            logger.warning(f"⚠️ No audio data received for story {story_id}")
            audio_metadata = {
                "_audio_ready": False,
                "_priority": "audio_failed",
                "error": "No audio data received",
                "generated_at": datetime.now(timezone.utc)
            }
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$set": {
                        "audio_metadata": audio_metadata,
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )

    except Exception as e:
        logger.error(f"❌ Failed to generate story audio for {story_id}: {e}")
        audio_metadata = {
            "_audio_ready": False,
            "_priority": "audio_failed",
            "error": str(e),
            "generated_at": datetime.now(timezone.utc)
        }
        await current_user.async_db.story_steps.update_one(
            {"_id": story_id},
            {
                "$set": {
                    "audio_metadata": audio_metadata,
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )


async def _generate_options_audio(current_user: UserTenantDB, task_item: Dict[str, Any], task_item_id: ObjectId):
    """
    Generate audio for each option in a task that has options and update database individually.

    Args:
        current_user: Current user context
        task_item: Task item dictionary containing question with options
        task_item_id: ObjectId of the task item for database updates
    """
    try:
        question = task_item.get("question", {})
        options = question.get("options", {})

        # Only generate audio for tasks that have options
        if not options or not isinstance(options, dict):
            logger.debug(f"No options found for task {task_item.get('_id', 'unknown')}, skipping audio generation")
            return

        logger.info(f"🔊 Generating audio for {len(options)} options in task {task_item_id}")

        # Generate audio for each option and update database individually
        for option_key, option_text in options.items():
            try:
                # Clean the option text (remove emojis and extra spaces)
                clean_text = option_text.strip()
                # Remove emojis by keeping only Nepali/Devanagari characters and basic punctuation
                import re
                clean_text = re.sub(r'[^\u0900-\u097F\s\.,!?]', '', clean_text).strip()

                if not clean_text:
                    logger.warning(f"Empty text after cleaning for option {option_key}: '{option_text}'")
                    # Update database with error for this option
                    await _update_option_metadata_in_db(
                        current_user, task_item_id, option_key, {
                            "text": option_text,
                            "clean_text": clean_text,
                            "audio_file_info": None,
                            "error": "Empty text after cleaning",
                            "generated_at": datetime.now(timezone.utc).isoformat()
                        }
                    )
                    continue

                logger.debug(f"Generating audio for option {option_key}: '{clean_text}'")

                # Generate audio using the existing audio generation function
                _file_text, file_info, usage_metadata = await generate_audio(
                    current_user,
                    clean_text,
                    "audio_prompt"
                )

                if file_info:
                    # Update database with successful audio generation for this option
                    option_metadata = {
                        "text": option_text,
                        "clean_text": clean_text,
                        "audio_file_info": file_info,
                        "usage_metadata": usage_metadata.model_dump() if hasattr(usage_metadata, 'model_dump') else usage_metadata,
                        "generated_at": datetime.now(timezone.utc).isoformat()
                    }
                    await _update_option_metadata_in_db(current_user, task_item_id, option_key, option_metadata)
                    logger.info(f"✅ Generated and saved audio for option {option_key}: {file_info.get('url')}")
                else:
                    logger.error(f"❌ Failed to generate audio for option {option_key}: No file info returned")
                    # Update database with error for this option
                    option_metadata = {
                        "text": option_text,
                        "clean_text": clean_text,
                        "audio_file_info": None,
                        "error": "No file info returned",
                        "generated_at": datetime.now(timezone.utc).isoformat()
                    }
                    await _update_option_metadata_in_db(current_user, task_item_id, option_key, option_metadata)

            except Exception as option_error:
                logger.error(f"❌ Error generating audio for option {option_key}: {option_error}")
                # Update database with error for this option
                option_metadata = {
                    "text": option_text,
                    "audio_file_info": None,
                    "error": str(option_error),
                    "generated_at": datetime.now(timezone.utc).isoformat()
                }
                await _update_option_metadata_in_db(current_user, task_item_id, option_key, option_metadata)

    except Exception as e:
        logger.error(f"❌ Error in _generate_options_audio: {e}")


@retry_db_operation(max_retries=3, delay=1.0, backoff=2.0)
async def _update_option_metadata_in_db(
    current_user: UserTenantDB,
    task_item_id: ObjectId,
    option_key: str,
    option_metadata: Dict[str, Any]
):
    """
    Update individual option metadata in the database.

    Args:
        current_user: Current user context
        task_item_id: ObjectId of the task item
        option_key: Option key (a, b, c, d, etc.)
        option_metadata: Metadata for this specific option
    """
    try:
        # Update the specific option metadata in the database
        update_result = await current_user.async_db.task_items.update_one(
            {"_id": task_item_id},
            {
                "$set": {
                    f"question.metadata.options_metadata.{option_key}": option_metadata,
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        if update_result.modified_count > 0:
            logger.debug(f"✅ Updated option {option_key} metadata for task {task_item_id}")
        else:
            logger.warning(f"⚠️ No document updated for option {option_key} in task {task_item_id}")

    except Exception as db_error:
        logger.error(f"❌ Failed to update option {option_key} metadata for task {task_item_id}: {db_error}")
        raise db_error


async def _send_websocket_notification(socketio_server, event_name: str, data: Dict[str, Any]):
    """Helper function to send WebSocket notifications."""
    if not socketio_server:
        return

    try:
        # Get the actual AsyncServer instance for emitting
        sio_instance = None
        if hasattr(socketio_server, 'sio'):
            sio_instance = socketio_server.sio
        elif hasattr(socketio_server, 'emit'):
            sio_instance = socketio_server
        else:
            logger.error(f"❌ Unknown socketio_server type: {type(socketio_server)}")
            return

        if sio_instance:
            await sio_instance.emit(event_name, data)
            logger.debug(f"✅ Sent WebSocket notification: {event_name}")
    except Exception as ws_error:
        logger.error(f"❌ Failed to send WebSocket notification {event_name}: {ws_error}")


async def process_audio_with_prompt_maker_v2(
    current_user: UserTenantDB,
    audio_bytes: bytes,
    num_tasks: int = 4
) -> Dict[str, Any]:
    """
    Process audio with the optimized V2 prompt maker.
    
    Args:
        current_user: Current user context
        audio_bytes: Audio data to process
        num_tasks: Number of tasks to generate
        
    Returns:
        Parsed tasks data from prompt_maker_v2.py
    """
    try:
        logger.info(f"Processing {len(audio_bytes)} bytes of audio with prompt_maker V2")

        # Call the V2 prompt maker with retry logic
        result = await retry_with_exponential_backoff(
            generate_v2,
            audio_bytes,
            num_tasks,
            current_user
        )
        
        if not result or not result.get("tasks"):
            error_msg = "No task items returned from prompt_maker_v2.generate"
            logger.error(error_msg)
            return {
                "tasks": [],
                "error": error_msg,
                "status": "error",
                "optimization_stats": {},
                "usage_metadata": {}
            }

        logger.info(f"✅ Generated {len(result['tasks'])} tasks with V2 optimizations")
        return result

    except Exception as e:
        logger.error(f"❌ Error processing audio with prompt_maker V2: {e}")
        return {
            "tasks": [],
            "error": str(e),
            "status": "error",
            "optimization_stats": {},
            "usage_metadata": {}
        }


# Removed process_audio_with_story_generator_v2 function
# Stories are now generated together with tasks in process_audio_with_prompt_maker_v2


def convert_to_socketio_format_v2(tasks_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Convert V2 tasks data to Socket.IO format.
    
    This maintains compatibility with existing Socket.IO clients while
    including V2 optimization metadata.
    """
    try:
        tasks = tasks_data.get("tasks", [])
        _optimization_stats = tasks_data.get("optimization_stats", {})
        
        socketio_tasks = []
        for task in tasks:
            socketio_task = {
                "id": str(task.get("id", ObjectId())),
                "type": task.get("type", "single_choice"),
                "title": task.get("title", "Generated Task"),
                "question": task.get("question", {}),
                "total_score": task.get("total_score", 10),
                "difficulty_level": task.get("difficulty_level", 2),
                "status": task.get("status", "pending"),
                # V2 specific fields
                "_v2_optimized": task.get("_v2_optimized", False),
                "_media_excluded": task.get("_media_excluded", False)
            }
            
            # Add story if present and not optimized away
            if task.get("story") and not task.get("_media_excluded", False):
                socketio_task["story"] = task["story"]
            
            socketio_tasks.append(socketio_task)
        
        logger.info(f"Converted {len(socketio_tasks)} tasks to Socket.IO format with V2 optimizations")
        return socketio_tasks
        
    except Exception as e:
        logger.error(f"Error converting tasks to Socket.IO format: {e}")
        return []


# Alias for backward compatibility
save_task_collection_and_items = save_task_collection_and_items_with_priority
