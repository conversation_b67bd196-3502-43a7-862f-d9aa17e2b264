"""
Routes for task set management.
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import asyncio

from app.shared.security import get_tenant_info
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.v1.api.management_service.storage import TaskManager, TaskSetFilter
from app.shared.db_enums import TaskStatus, InputType, DifficultyLevel, VerificationStatus

# from app.v1.api.management_service.models.responses import APIResponse, ResponseMetadata
from bson import ObjectId

# Configure logging
loggers = setup_new_logging(__name__)

router = APIRouter()





@router.get("/filtered")
async def get_task_sets(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search query"),
    parent_task_set_id: Optional[str] = Query(None, description="Parent task set ID"),
    start_date: Optional[datetime] = Query(None, description="Start date for filtering"),
    end_date: Optional[datetime] = Query(None, description="End date for filtering"),
    status: Optional[TaskStatus] = Query(None, description="Status filter"),
    difficulty_level: Optional[int] = Query(None, ge=1, le=3, description="Difficulty level filter (1=easy, 2=medium, 3=hard)"),
    input_type: Optional[str] = Query(None, description="Input type filter (audio, text, video, image)"),
    sort_by: str = Query("created_at", description="Field to sort by"),
    sort_order: int = Query(-1, ge=-1, le=1, description="Sort order (1 for ascending, -1 for descending)"),
    gentype: Optional[str] = Query(None, description="Generation type filter (primary, follow_up, or None for both)"),
    fields: Optional[List[str]] = Query(None, description="Fields to retrieve"),
    user_tenant: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get the current user's task sets with filtering, sorting, and pagination.

    Supports filtering by:
    - difficulty_level: Filter by difficulty level (1=easy, 2=medium, 3=hard)
    - input_type: Filter by input type (audio, text, video, image)
    - status: Filter by task set status
    - gentype: Filter by generation type (primary, follow_up, or None for both)
    - date range: Filter by creation date range
    - search: Search in user_id and input_type fields

    Supports sorting by created_at in ascending (1) or descending (-1) order.

    Returns:
        Paginated list of task sets with default fields: user_id, input_type, tasks,
        created_at, status, total_score, scored, total_tasks, source, difficulty_level, attempted_tasks
    """
    user_id = str(user_tenant.user.id)
    try:
        loggers.info(
            f"Getting filtered task sets for user {user_id}, page={page}, limit={limit}"
        )

        # Create filter parameters
        filter_params = TaskSetFilter(
            page=page,
            limit=limit,
            search=search,
            parent_task_set_id=parent_task_set_id,
            start_date=start_date,
            end_date=end_date,
            status=status,
            difficulty_level=difficulty_level,
            input_type=input_type,
            sort_by=sort_by,
            sort_order=sort_order,
            fields_to_retrieve=fields,
            gentype=gentype
        )

        task_manager = TaskManager(current_user=user_tenant)

        # Get filtered task sets using the new method
        result = await task_manager.get_filtered_task_sets(filter_params=filter_params)

        if "error" in result:
            loggers.error(f"Error getting filtered task sets: {result['error']}")
            raise HTTPException(status_code=500, detail=result["error"])

        return result
    except Exception as e:
        loggers.error(f"Error getting filtered task sets: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/filters/values")
async def get_task_set_filter_values(
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> Dict[str, Any]:
    """
    Get actual filter values from the database for task sets.

    Returns:
        Dictionary containing filter values:
        - status: List of available status values
        - difficulty_level: List of available difficulty levels
        - source: List of available source values
        - sort_by: Available sort fields
        - sort_type: Available sort orders (asc/desc)
    """
    
    try:
        loggers.info(f"Getting task set filter values for user {user_tenant.user.id}")

        task_manager = TaskManager(current_user=user_tenant)

        # Get actual filter values from database
        result = await task_manager.get_task_set_filter_values()

        if "error" in result:
            loggers.error(f"Error getting task set filter values: {result['error']}")
            raise HTTPException(status_code=500, detail=result["error"])

        # Format response with consistent naming
        filter_values = {
            "status": result.get("status_values", []),
            "difficulty_level": result.get("difficulty_values", []),
            "source": result.get("source_values", []),
            "sort_by": ["created_at"],
            "sort_type": ["asc", "desc"]
        }

        return filter_values

    except Exception as e:
        loggers.error(f"Error getting task set filter values: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_set_id}")
async def get_task_set(
    task_set_id: str,
    include_tasks: bool = Query(False, description="Include tasks in response"),
    include_stories: bool = Query(False, description="Include stories in response"),
    fields: Optional[List[str]] = Query(None, description="Fields to retrieve"),
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> Dict[str, Any]:
    """
    Get a task set by ID with control over what fields are returned.

    Args:
        task_set_id: The task set ID
        include_tasks: Whether to include tasks in response
        include_stories: Whether to include stories in response
        fields: Fields to retrieve
        user_tenant: User tenant information

    Returns:
        Task set details with optional tasks and stories
    """
    user_id = str(user_tenant.user.id)
    try:
        loggers.info(f"Getting task set {task_set_id} for user {user_id}")

        task_manager = TaskManager(current_user=user_tenant)

        # Get task set with specified fields
        task_set = await task_manager.get_task_set(
            task_set_id=task_set_id,
            include_tasks=include_tasks,
            fields_to_retrieve=fields
        )

        if "error" in task_set:
            loggers.error(f"Error getting task set: {task_set['error']}")
            raise HTTPException(status_code=404, detail=task_set["error"])

        # Include stories if requested
        if include_stories:
            try:
                # Query stories by task_set_id, collection_id, or v2_collection_id
                query = {"$or": [
                    {"task_set_id": task_set_id},
                    {"collection_id": task_set_id},
                    {"v2_collection_id": task_set_id}
                ]}

                cursor = user_tenant.async_db.story_steps.find(query)
                stories = await cursor.to_list(length=None)

                # Convert ObjectIds to strings and generate presigned URLs in parallel
                async def process_story_urls(story):
                    """Process a single story's URLs in parallel."""
                    story["id"] = str(story.pop("_id"))

                    async def generate_metadata_url():
                        """Generate presigned URL for metadata."""
                        if "metadata" in story and "object_name" in story["metadata"]:
                            try:
                                presigned_url = user_tenant.minio.get_presigned_url(
                                    bucket_name=user_tenant.minio_bucket_name,
                                    object_name=story["metadata"]["object_name"],
                                    expires=timedelta(hours=24),
                                    method="GET"
                                )
                                story["metadata"]["url"] = presigned_url
                            except Exception as e:
                                loggers.error(f"Error generating metadata URL for {story['metadata']['object_name']}: {e}")
                                story["metadata"]["url"] = None

                    async def generate_audio_metadata_url():
                        """Generate presigned URL for audio_metadata."""
                        if "audio_metadata" in story and "object_name" in story["audio_metadata"]:
                            try:
                                presigned_url = user_tenant.minio.get_presigned_url(
                                    bucket_name=user_tenant.minio_bucket_name,
                                    object_name=story["audio_metadata"]["object_name"],
                                    expires=timedelta(hours=24),
                                    method="GET"
                                )
                                story["audio_metadata"]["url"] = presigned_url
                            except Exception as e:
                                loggers.error(f"Error generating audio metadata URL for {story['audio_metadata']['object_name']}: {e}")
                                story["audio_metadata"]["url"] = None

                    # Generate both URLs in parallel
                    await asyncio.gather(
                        generate_metadata_url(),
                        generate_audio_metadata_url(),
                        return_exceptions=True
                    )

                # Process all stories in parallel
                await asyncio.gather(*[process_story_urls(story) for story in stories], return_exceptions=True)

                task_set["stories"] = stories
                task_set["stories_count"] = len(stories)
                loggers.info(f"Added {len(stories)} stories to task set {task_set_id}")
            except Exception as e:
                loggers.error(f"Error fetching stories for task set {task_set_id}: {e}")
                task_set["stories"] = []
                task_set["stories_count"] = 0

        return task_set
    except Exception as e:
        loggers.error(f"Error getting task set: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# score
@router.get("/score/{task_set_id}")
async def get_task_set_score(
    task_set_id: str,
    user_tenant: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get the score for a task set.

    Args:
        task_set_id: The task set ID
        user_tenant: Current user information

    Returns:
        The score for the task set
    """
    user_id = str(user_tenant.user.id)
    try:
        loggers.info(f"Getting score for task set {task_set_id} for user {user_id}")
        
        # Validate task_set_id
        if not task_set_id or not ObjectId.is_valid(task_set_id):
            raise HTTPException(status_code=400, detail="Invalid task set ID")

        # Get the task set
        task_set = await user_tenant.async_db.task_sets.find_one(
            {"_id": ObjectId(task_set_id)},
            {"scored": 1, "total_score": 1,"total_tasks": 1,"attempted_tasks": 1,"status": 1}  # Use 'total_score' consistently
        )

        if not task_set:
            raise HTTPException(status_code=404, detail=f"Task set {task_set_id} not found")

        return {
            "scored": task_set["scored"],
            "total_score": task_set["total_score"],  # Use 'total_score' consistently
            "percentage": task_set["scored"] / task_set["total_score"] * 100,
            "total_tasks": task_set["total_tasks"],
            "attempted_tasks": task_set["attempted_tasks"],
            "status": task_set["status"]
        }
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error getting task set score: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting task set score: {str(e)}")


@router.post("/retry/{task_set_id}")
async def retry_task_set(
    task_set_id: str,
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> Dict[str, Any]:
    """
    Reset a task set to allow retry - clears all scores and completion status.

    This endpoint:
    1. Resets all task items in the task set (scores, answers, completion status)
    2. Resets the task set scores and status
    3. Allows the user to attempt the entire task set fresh

    Args:
        task_set_id: The task set ID to retry
        user_tenant: Current user information

    Returns:
        Success message with reset statistics
    """
    user_id = str(user_tenant.user.id)

    try:
        loggers.info(f"Retrying task set {task_set_id} for user {user_id}")

        # Validate task_set_id
        if not task_set_id or not ObjectId.is_valid(task_set_id):
            raise HTTPException(status_code=400, detail="Invalid task set ID")

        # Get the task set and verify ownership
        task_set = await user_tenant.async_db.task_sets.find_one(
            {"_id": ObjectId(task_set_id)},
            {"user_id": 1, "tasks": 1, "total_tasks": 1}
        )

        if not task_set:
            raise HTTPException(status_code=404, detail=f"Task set {task_set_id} not found")

        # Verify ownership
        if str(task_set.get("user_id")) != user_id:
            raise HTTPException(status_code=403, detail="You do not have permission to retry this task set")

        # Reset all task items in the task set
        task_ids = task_set.get("tasks", [])
        if task_ids:
            # Convert string IDs to ObjectIds if needed
            object_ids = [ObjectId(tid) if isinstance(tid, str) else tid for tid in task_ids]

            # Comprehensive reset of ALL task item fields to pristine state
            reset_fields = {
                # Core scoring and submission fields
                "scored": 0,
                "user_answer": None,
                "answered_at": None,
                "is_attempted": False,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,

                # Status and result fields
                "status": TaskStatus.PENDING,  # Reset to initial status using proper enum
                "result": None,
                "remark": None,

                # User reference fields (clear submission tracking)
                "submitted_by": None,

                # Verification fields (reset verification state)
                "verification_status": VerificationStatus.PENDING,  # Reset to default verification status using proper enum
                "verification_notes": None,
                "verified_at": None,
                "verified_by": None,

                # Testing fields (clear any test results)
                "test_status": None,
                "test_results": None,

                # Metadata update
                "updated_at": datetime.now()
            }

            # Reset all task items with comprehensive field reset
            task_reset_result = await user_tenant.async_db.task_items.update_many(
                {"_id": {"$in": object_ids}},
                {"$set": reset_fields}
            )

            loggers.info(f"Reset {task_reset_result.modified_count} task items for task set {task_set_id}")

        # Reset the task set itself with comprehensive field reset
        task_set_reset_fields = {
            "scored": 0,
            "attempted_tasks": 0,
            "status": TaskStatus.PENDING,
            "completed_at": None,
            "submitted_at": None,  # Reset submission timestamp
            "updated_at": datetime.now(),
            "remark": "Task set reset for retry - all fields cleared"
        }

        # Also clear the attempted_tasks_list if it exists
        task_set_reset_result = await user_tenant.async_db.task_sets.update_one(
            {"_id": ObjectId(task_set_id)},
            {
                "$set": task_set_reset_fields,
                "$unset": {"attempted_tasks_list": ""}  # Remove the attempted tasks list
            }
        )

        if task_set_reset_result.modified_count == 0:
            raise HTTPException(status_code=500, detail="Failed to reset task set")

        loggers.info(f"Successfully reset task set {task_set_id} and {task_reset_result.modified_count} task items")

        return {
            "message": "Task set and all task items reset successfully - all fields cleared",
            "task_set_id": task_set_id,
            "reset_details": {
                "tasks_reset": task_reset_result.modified_count if task_ids else 0,
                "total_tasks": len(task_ids) if task_ids else 0,
                "task_item_fields_reset": [
                    "scored", "user_answer", "answered_at", "is_attempted",
                    "submitted", "submitted_at", "attempts_count", "status",
                    "result", "remark", "submitted_by", "verification_status",
                    "verification_notes", "verified_at", "verified_by",
                    "test_status", "test_results"
                ],
                "task_set_fields_reset": [
                    "scored", "attempted_tasks", "status", "completed_at",
                    "submitted_at", "attempted_tasks_list", "remark"
                ]
            },
            "reset_at": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error retrying task set {task_set_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrying task set: {str(e)}")