#!/usr/bin/env python3
"""
Test script for the options audio generation feature.

This script tests the new feature that generates audio for each option
in questions that have multiple choice options.
"""

import asyncio
import sys
import os
from datetime import datetime, timezone
from bson.objectid import ObjectId

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.v2.api.socket_service_v2.generator.task_utils_v2 import _generate_options_audio


class MockUser:
    """Mock user class for testing."""
    def __init__(self):
        self.user = MockUserData()
        self.minio = MockMinioConfig()
        self.async_db = None


class MockUserData:
    """Mock user data."""
    def __init__(self):
        self.id = "test_user_id"


class MockMinioConfig:
    """Mock MinIO configuration."""
    def __init__(self):
        self.endpoint = "localhost:9000"
        self.access_key = "test_access_key"
        self.secret_key = "test_secret_key"


async def test_options_audio_generation():
    """Test the options audio generation feature."""
    print("🧪 Testing options audio generation feature...")
    
    # Create a mock user
    mock_user = MockUser()
    
    # Create a sample task item with options
    task_item = {
        "_id": ObjectId(),
        "task_set_id": ObjectId(),
        "user_id": ObjectId(),
        "type": "single_choice",
        "title": "Test Question",
        "question": {
            "text": "नेपालको राजधानी कुन हो?",
            "translated_text": "What is the capital of Nepal?",
            "options": {
                "a": "काठमाडौं 🏛️",
                "b": "पोखरा 🏔️", 
                "c": "चितवन 🌳",
                "d": "भक्तपुर 🏛️"
            },
            "answer_hint": "Nepal's capital city",
            "metadata": {}
        },
        "correct_answer": {
            "value": "a",
            "type": "single"
        },
        "total_score": 10,
        "difficulty_level": 1,
        "metadata": {
            "_v2_optimized": False,
            "_media_excluded": False,
            "_priority": "instant",
            "_media_ready": True
        },
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc)
    }
    
    print(f"📋 Test task: {task_item['question']['text']}")
    print(f"📝 Options: {task_item['question']['options']}")
    
    try:
        # Test the options audio generation function
        await _generate_options_audio(mock_user, task_item)
        
        # Check if options_metadata was added
        options_metadata = task_item["question"]["metadata"].get("options_metadata", {})
        
        if options_metadata:
            print(f"✅ Success! Generated audio metadata for {len(options_metadata)} options:")
            for option_key, metadata in options_metadata.items():
                print(f"   Option {option_key}: {metadata.get('clean_text', 'N/A')}")
                if metadata.get('audio_file_info'):
                    print(f"      Audio URL: {metadata['audio_file_info'].get('url', 'N/A')}")
                elif metadata.get('error'):
                    print(f"      Error: {metadata['error']}")
        else:
            print("❌ No options metadata generated")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()


async def test_task_without_options():
    """Test with a task that has no options."""
    print("\n🧪 Testing task without options...")
    
    mock_user = MockUser()
    
    # Create a task item without options
    task_item = {
        "_id": ObjectId(),
        "question": {
            "text": "Test question without options",
            "metadata": {}
        }
    }
    
    try:
        await _generate_options_audio(mock_user, task_item)
        print("✅ Successfully handled task without options")
    except Exception as e:
        print(f"❌ Error handling task without options: {e}")


async def main():
    """Main test function."""
    print("🚀 Starting options audio generation tests...\n")
    
    # Test 1: Normal task with options
    await test_options_audio_generation()
    
    # Test 2: Task without options
    await test_task_without_options()
    
    print("\n✅ All tests completed!")


if __name__ == "__main__":
    # Note: This test will fail with actual audio generation due to missing API keys
    # and MinIO setup, but it will test the logic and structure
    print("⚠️  Note: This test will show the structure but may fail on actual audio generation")
    print("   due to missing API keys and MinIO configuration.\n")
    
    asyncio.run(main())
